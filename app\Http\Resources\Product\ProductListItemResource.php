<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Product models into API responses for product listing.
 * 
 * This resource is specifically designed for product index/listing pages where we need
 * minimal product information for performance optimization.
 */
class ProductListItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Basic product information (title, slug)
     * - Product rating
     * - Main product image
     * - Price information (regular and sale price)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Get the default variation for pricing
        $defaultVariation = $this->variations->first();

        // Use stored product rating
        $productAverageRate = $this->rating ?? 0;

        // Get the main image
        $mainImage = $this->gallery->first();

        // Determine the display price (use sale_price if available and lower than regular price)
        $price = $defaultVariation ? $defaultVariation->price : 0;
        $salePrice = $defaultVariation ? $defaultVariation->sale_price : null;
        $displayPrice = ($salePrice && $salePrice < $price) ? $salePrice : $price;

        return [
            'title' => $this->title,
            'slug' => $this->slug,
            'rate' => $productAverageRate,
            'image' => $mainImage ? [
                'url' => $mainImage->image_url,
                'caption' => $mainImage->caption,
            ] : null,
            'price' => (int) $displayPrice,
            'sale_price' => $salePrice && $salePrice < $price ? (int) $salePrice : null,
        ];
    }
}
