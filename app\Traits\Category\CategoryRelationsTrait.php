<?php

namespace App\Traits\Category;

use App\Models\Product\Attribute;
use App\Models\Product\Category;
use App\Models\Product\Product;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\BelongsToMany;
use MongoDB\Laravel\Relations\HasMany;

/**
 * Category Relations Trait
 *
 * This trait contains all relationship methods for the Category model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Category
 */
trait CategoryRelationsTrait
{
    /**
     * Get the parent category of this category.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id', '_id');
    }

    /**
     * Get the child categories of this category.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id', '_id');
    }

    /**
     * Get the products that belong to this category.
     *
     * @return \MongoDB\Laravel\Relations\BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'category_product', 'category_id', 'product_id');
    }

    /**
     * Get the attributes that belong to this category.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function attributes(): HasMany
    {
        return $this->hasMany(Attribute::class, 'category_id', '_id');
    }

    /**
     * Get only searchable parent attributes for this category.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function searchableAttributes(): HasMany
    {
        return $this->hasMany(Attribute::class, 'category_id', '_id')
            ->where('searchable', true)
            ->whereNull('parent_id');
    }

    /**
     * Get all child attribute values for searchable parent attributes belonging to this category.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function searchableValues(): HasMany
    {
        return $this->hasMany(Attribute::class, 'category_id', '_id')
            ->whereNotNull('parent_id')
            ->whereHas('parent', function ($query) {
                $query->where('searchable', true);
            });
    }

    /**
     * Get all searchable attributes for this category and all its descendants recursively.
     * This includes both the parent attributes and their values.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllSearchableAttributesRecursively()
    {
        // Get all category IDs recursively (current + all descendants)
        $allCategoryIds = $this->getAllDescendantIds();

        // Get all searchable parent attributes for these categories with their values
        return Attribute::whereIn('category_id', $allCategoryIds)
            ->where('searchable', true)
            ->whereNull('parent_id') // Only parent attributes (titles)
            ->with('values') // Load attribute values
            ->get();
    }

    /**
     * Get all descendant category IDs including this category.
     *
     * @return array
     */
    public function getAllDescendantIds(): array
    {
        $categoryIds = [$this->_id];

        // Get direct children
        $children = $this->children()->get(['_id']);

        // Recursively get children of children
        foreach ($children as $child) {
            $categoryIds = array_merge(
                $categoryIds,
                $child->getAllDescendantIds()
            );
        }

        return array_unique($categoryIds);
    }
}
