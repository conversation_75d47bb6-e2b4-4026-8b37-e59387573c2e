<?php

namespace App\Http\Controllers\Api\V1\Private\User;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Address\GetUserAddresses;
use App\Services\Actions\Address\CreateAddress;
use App\Services\Actions\Address\UpdateAddress;
use App\Services\Actions\Address\DeleteAddress;
use App\Http\Resources\User\AddressResource;
use App\Http\Requests\Address\StoreAddressRequest;
use App\Http\Requests\Address\UpdateAddressRequest;
use App\Http\Requests\Address\DeleteAddressRequest;
use App\Http\Requests\Address\GetUserAddressesRequest;

/**
 * Controller for managing user addresses
 *
 * @group Address Management
 * @authenticated
 */
class AddressController extends BaseController
{
    /**
     * Get all addresses for the authenticated user
     *
     * @param GetUserAddressesRequest $request The validated request
     * @param GetUserAddresses $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetUserAddressesRequest $request, GetUserAddresses $action)
    {
        $addresses = $action->handle($request->validated());

        return $this->sendResponse(
            AddressResource::collection($addresses),
            __('messages.address.retrieved')
        );
    }

    /**
     * Create a new address for the authenticated user
     *
     * @param StoreAddressRequest $request The validated request
     * @param CreateAddress $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreAddressRequest $request, CreateAddress $action)
    {
        $address = $action->handle($request->validated());

        return $this->sendResponse(
            new AddressResource($address),
            __('messages.address.created')
        );
    }

    /**
     * Update an existing address for the authenticated user
     *
     * @param UpdateAddressRequest $request The validated request
     * @param string $id The address ID
     * @param UpdateAddress $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateAddressRequest $request, string $id, UpdateAddress $action)
    {
        $address = $action->handle($request->validated());

        return $this->sendResponse(
            new AddressResource($address),
            __('messages.address.updated')
        );
    }

    /**
     * Delete an address for the authenticated user
     *
     * @param DeleteAddressRequest $request The validated request
     * @param string $id The address ID
     * @param DeleteAddress $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(DeleteAddressRequest $request, DeleteAddress $action)
    {
        $action->handle($request->validated());

        return $this->sendResponse(
            null,
            __('messages.address.deleted')
        );
    }
}
