<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Product\ProductAttributesResource;

/**
 * Resource class for transforming InvoiceProduct models into API responses.
 *
 * Maintains consistency with CartItemResource structure for uniform product representation.
 */
class InvoiceProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Get the product variation if loaded
        $variant = $this->whenLoaded('productVariation');
        $variant = $variant instanceof \App\Models\Product\ProductVariation ? $variant : null;

        // Get the product from the variant if available
        $product = $variant ? $variant->product : null;

        return [
            'product_id' => $this->product_id,
            'id' => $this->variant_id,
            'name' => $this->name,
            'sku' => $variant ? $variant->sku : null,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'total' => $this->total,
            'image' => $this->image ?: ($product && $product->gallery()->first() ? $product->gallery()->first()->image_url : null),
            'in_stock' => $variant ? ($variant->current_quantity >= $this->quantity) : true,
            'attributes' => $variant ? ProductAttributesResource::collection($variant->attributes) : [],
        ];
    }
}
