<?php

namespace Database\Seeders;

use App\Models\Product\Attribute;
use App\Models\Product\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Seeder for creating product attributes and their values.
 *
 * Creates parent attributes (titles) and child attributes (values)
 * and associates them with existing categories.
 */
class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates parent attributes with their child values and associates
     * them with existing categories from the CategorySeeder.
     */
    public function run(): void
    {
        try {
            DB::transaction(function () {
                $this->createAttributes();
            });

            $this->command->info('Attributes created successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating attributes: ' . $e->getMessage());
            $this->command->error('Failed to create attributes: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create all attributes with their values and category associations.
     */
    private function createAttributes(): void
    {
        // Get some existing categories to associate with attributes
        $categories = Category::limit(5)->get();

        if ($categories->isEmpty()) {
            $this->command->warn('No categories found. Make sure to run CategorySeeder first.');
            return;
        }

        // Define attributes with their properties and values
        $attributesData = [
            [
                'title' => 'رنگ',
                'searchable' => true,
                'values' => ['قرمز', 'آبی', 'سبز', 'مشکی', 'سفید']
            ],
            [
                'title' => 'سایز',
                'searchable' => true,
                'values' => ['کوچک', 'متوسط', 'بزرگ', 'XL', 'XXL']
            ],
            [
                'title' => 'برند',
                'searchable' => true,
                'values' => ['سامسونگ', 'اپل', 'ال جی']
            ],
            [
                'title' => 'جنس',
                'searchable' => false,
                'values' => ['پنبه', 'پلی استر', 'چرم']
            ],
            [
                'title' => 'وزن',
                'searchable' => false,
                'values' => ['سبک', 'متوسط', 'سنگین']
            ]
        ];

        foreach ($attributesData as $attributeData) {
            $this->createAttributeWithValues($attributeData, $categories);
        }
    }

    /**
     * Create a parent attribute with its child values.
     *
     * @param array $attributeData
     * @param \Illuminate\Database\Eloquent\Collection $categories
     */
    private function createAttributeWithValues(array $attributeData, $categories): void
    {
        // Randomly select a category for this attribute
        $randomCategory = $categories->random();

        // Create or update the parent attribute
        $parentAttribute = Attribute::updateOrCreate(
            [
                'title' => $attributeData['title'],
                'parent_id' => null
            ],
            [
                'category_id' => $randomCategory->_id,
                'searchable' => $attributeData['searchable'],
                'value' => null
            ]
        );

        $this->command->info("Created parent attribute: {$attributeData['title']}");

        // Create child attributes (values)
        foreach ($attributeData['values'] as $valueTitle) {
            Attribute::updateOrCreate(
                [
                    'title' => $valueTitle,
                    'parent_id' => $parentAttribute->_id
                ],
                [
                    'category_id' => $randomCategory->_id,
                    'searchable' => false, // Child attributes are not searchable
                    'value' => null
                ]
            );
        }

        $this->command->info("Created " . count($attributeData['values']) . " values for {$attributeData['title']}");
    }
}
