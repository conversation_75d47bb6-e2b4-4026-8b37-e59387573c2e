<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming category product index data into API responses.
 * Extends the basic ProductIndexResource with category metadata.
 */
class CategoryProductIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Pagination data
     * - Total products count
     * - Min and max prices
     * - Products array with essential information
     * - Category metadata
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $products = $this->resource['products'];
        $category = $this->resource['category'];

        return [
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
            ],
            'total_products' => $this->resource['total_count'],
            'min_price' => $this->resource['min_price'],
            'max_price' => $this->resource['max_price'],
            'category' => [
                'title' => $category->title,
                'slug' => $category->slug,
                'type' => $category->type,
            ],
            'products' => ProductListItemResource::collection($products),
        ];
    }
}
