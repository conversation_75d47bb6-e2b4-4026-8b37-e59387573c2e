<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('products', function (Blueprint $collection) {
            $collection->string('title');
            $collection->text('description')->nullable();
            $collection->string('slug')->unique();
            $collection->string('meta_title')->nullable();
            $collection->string('meta_description')->nullable();
            $collection->string('shop_id')->nullable();
            $collection->index('shop_id');
            $collection->timestamps();
            $collection->createIndex([
                'title' => 'text',
                'description' => 'text',
                'meta_title' => 'text',
                'meta_description' => 'text'
            ], [
                'name' => 'products_text_index',
                'default_language' => 'none', // Disable language-specific stemming for Persian/multilingual content
                'weights' => [
                    'title' => 10,           // Highest weight for title matches
                    'meta_title' => 8,       // High weight for meta title
                    'description' => 5,      // Medium weight for description
                    'meta_description' => 3  // Lower weight for meta description
                ]
            ]);
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('products');
    }
};
