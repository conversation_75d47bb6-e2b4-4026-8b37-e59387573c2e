<?php

namespace App\Http\Controllers\Api\V1\Public\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\GetCategorySearchableAttributesRequest;
use App\Http\Resources\Product\CategoryAttributeResource;
use App\Services\Actions\Product\GetCategorySearchableAttributes;

/**
 * Controller for retrieving category information and related data
 *
 * @group Category Management
 */
class CategoryController extends BaseController
{
    /**
     * Display the specified category's searchable attributes
     *
     * Returns all searchable attributes for the given category
     * and recursively includes attributes from all child categories.
     *
     * @param GetCategorySearchableAttributesRequest $request The validated request
     * @param GetCategorySearchableAttributes $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function show(GetCategorySearchableAttributesRequest $request, GetCategorySearchableAttributes $action)
    {
        $attributes = $action->handle($request->validated());

        return $this->sendResponse(
            CategoryAttributeResource::collection($attributes),
            __('messages.category.searchable_attributes_found')
        );
    }
}
