<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Action class to retrieve searchable attributes for a category and its children recursively.
 */
class GetCategorySearchableAttributes
{
    /**
     * Retrieve all searchable attributes for a category and its children recursively.
     *
     * @param array $data Contains slug
     * @return Collection Collection of searchable attributes
     */
    public function handle(array $data): Collection
    {
        return DB::transaction(function () use ($data) {
            $slug = $data['slug'];

            // Find the category by slug
            $category = Category::where('slug', $slug)->first();
            if (!$category) {
                return collect();
            }

            // Use the model method to get all searchable attributes recursively
            return $category->getAllSearchableAttributesRecursively();
        });
    }

}
