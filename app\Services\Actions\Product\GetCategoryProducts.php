<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Action class to retrieve products by category with filtering, sorting, and pagination.
 */
class GetCategoryProducts
{
    private bool $hasTextSearch = false;
    private ?Category $category = null;

    /**
     * Handle the category products request with filters and pagination.
     *
     * @param array $data The validated request data
     * @return array Contains paginated products, total count, min/max prices, and category info
     */
    public function handle(array $data): array
    {
        return DB::transaction(function () use ($data) {
            // Find the category by slug
            $this->category = Category::where('slug', $data['slug'])->first();
            
            if (!$this->category) {
                throw new \Exception(__('messages.category.not_found'));
            }

            // Start with a clean base query
            $query = $this->buildBaseQuery();

            // Apply category filter (including hierarchical categories)
            $this->applyCategoryFilter($query);

            // Apply filters incrementally (same as GetAllProducts)
            $this->applySearchFilter($query, $data);
            $this->applyPriceRangeFilter($query, $data);
            $this->applyStockFilter($query, $data);
            $this->applyGuaranteeFilter($query, $data);
            $this->applyShopFilter($query, $data);

            // Store sort parameter for later use
            $sortParam = $data['sort'] ?? 'newest';

            // Apply sorting
            $this->applySorting($query, $sortParam);

            // Get total count before pagination
            $totalCount = $query->count();

            // Get min and max prices (optimized calculation)
            $priceStats = $this->getPriceStatistics();

            // For price sorting without text search, we need to handle it differently
            if (!$this->hasTextSearch && in_array($sortParam, ['cheapest', 'most_expensive'])) {
                // Get all products first, then sort by price in PHP
                $allProducts = $query->get();
                $sortedProducts = $this->sortProductsByPrice($allProducts, $sortParam);

                // Apply pagination manually
                $perPage = $data['per_page'] ?? 15;
                $page = $data['page'] ?? 1;
                $products = $this->paginateCollection($sortedProducts, $perPage, $page);
            } else {
                // Apply pagination normally
                $perPage = $data['per_page'] ?? 15;
                $page = $data['page'] ?? 1;
                $products = $query->paginate($perPage, ['*'], 'page', $page);
            }

            return [
                'products' => $products,
                'total_count' => $totalCount,
                'min_price' => $priceStats['min_price'],
                'max_price' => $priceStats['max_price'],
                'category' => $this->category,
            ];
        });
    }

    /**
     * Build the base query with essential relationships.
     *
     * @return Builder
     */
    private function buildBaseQuery(): Builder
    {
        return Product::with(['variations', 'gallery', 'guarantees', 'categories']);
    }

    /**
     * Apply category filter including hierarchical categories.
     *
     * @param Builder $query
     * @return void
     */
    private function applyCategoryFilter(Builder $query): void
    {
        // Get all child category IDs recursively
        $categoryIds = $this->getAllCategoryIds($this->category);

        // Filter products that belong to any of these categories
        $query->whereHas('categories', function ($categoryQuery) use ($categoryIds) {
            $categoryQuery->whereIn('_id', $categoryIds);
        });
    }

    /**
     * Get all category IDs including children recursively.
     *
     * @param Category $category
     * @return array
     */
    private function getAllCategoryIds(Category $category): array
    {
        $categoryIds = [(string) $category->_id];
        
        // Get all children recursively
        $children = $category->children()->get();
        foreach ($children as $child) {
            $categoryIds = array_merge($categoryIds, $this->getAllCategoryIds($child));
        }

        return $categoryIds;
    }

    /**
     * Apply search filter with text search and relevance scoring.
     * (Same implementation as GetAllProducts)
     */
    private function applySearchFilter(Builder $query, array $data): void
    {
        if (!empty($data['search'])) {
            $this->hasTextSearch = true;

            // Use MongoDB text search with proper escaping
            $searchTerm = trim($data['search']);
            $escapedSearch = str_replace('"', '\"', $searchTerm);

            $query->where('$text', ['$search' => $escapedSearch]);

            // Project the text score for relevance sorting
            $query->project([
                '*',
                'score' => ['$meta' => 'textScore']
            ]);
        }
    }

    /**
     * Apply price range filter considering both regular and sale prices (MongoDB-compatible).
     * (Same implementation as GetAllProducts)
     */
    private function applyPriceRangeFilter(Builder $query, array $data): void
    {
        if (!isset($data['min_price']) && !isset($data['max_price'])) {
            return;
        }

        $query->whereHas('variations', function ($variationQuery) use ($data) {
            if (isset($data['min_price'])) {
                $minPrice = (int) $data['min_price'];

                $variationQuery->where(function ($priceQuery) use ($minPrice) {
                    $priceQuery->where(function ($saleQuery) use ($minPrice) {
                        $saleQuery->whereNotNull('sale_price')
                            ->where('sale_price', '>=', $minPrice);
                    })
                        ->orWhere(function ($regularQuery) use ($minPrice) {
                            $regularQuery->whereNull('sale_price')
                                ->where('price', '>=', $minPrice);
                        });
                });
            }

            if (isset($data['max_price'])) {
                $maxPrice = (int) $data['max_price'];

                $variationQuery->where(function ($priceQuery) use ($maxPrice) {
                    $priceQuery->where(function ($saleQuery) use ($maxPrice) {
                        $saleQuery->whereNotNull('sale_price')
                            ->where('sale_price', '<=', $maxPrice);
                    })
                        ->orWhere(function ($regularQuery) use ($maxPrice) {
                            $regularQuery->whereNull('sale_price')
                                ->where('price', '<=', $maxPrice);
                        });
                });
            }
        });
    }

    /**
     * Apply stock availability filter using the ledger-based inventory system.
     * (Same implementation as GetAllProducts)
     */
    private function applyStockFilter(Builder $query, array $data): void
    {
        if (empty($data['in_stock_only']) || $data['in_stock_only'] !== 'true') {
            return;
        }

        $query->whereHas('variations', function ($variationQuery) {
            $variationQuery->whereHas('purchases', function ($purchaseQuery) {
                $purchaseQuery->where('quantity', '>', 0);
            });
        });
    }

    /**
     * Apply guarantee availability filter.
     * (Same implementation as GetAllProducts)
     */
    private function applyGuaranteeFilter(Builder $query, array $data): void
    {
        if (empty($data['has_guarantee_only']) || $data['has_guarantee_only'] !== 'true') {
            return;
        }

        $query->whereHas('guarantees');
    }

    /**
     * Apply shop filter for filtering products by shop.
     * (Same implementation as GetAllProducts)
     */
    private function applyShopFilter(Builder $query, array $data): void
    {
        if (empty($data['shop_id'])) {
            return;
        }

        $query->where('shop_id', $data['shop_id']);
    }

    /**
     * Apply sorting to the query based on the sort parameter.
     * (Same implementation as GetAllProducts)
     */
    private function applySorting(Builder $query, string $sort): void
    {
        if ($this->hasTextSearch) {
            $query->orderBy('score', 'desc');

            switch ($sort) {
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'cheapest':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'most_expensive':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'most_sales':
                case 'most_popular':
                    $query->withCount('comments')->orderBy('comments_count', 'desc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
            return;
        }

        switch ($sort) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'cheapest':
                $this->applyCheapestSort($query);
                break;
            case 'most_expensive':
                $this->applyMostExpensiveSort($query);
                break;
            case 'most_sales':
                $this->applyMostSalesSort($query);
                break;
            case 'most_viewed':
                $query->orderBy('created_at', 'desc');
                break;
            case 'most_popular':
                $this->applyMostPopularSort($query);
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }
    }

    /**
     * Apply cheapest price sorting using a hybrid approach for MongoDB compatibility.
     */
    private function applyCheapestSort(Builder $query): void
    {
        $query->with(['variations' => function ($variationQuery) {
            $variationQuery->select('product_id', 'price', 'sale_price');
        }]);
    }

    /**
     * Apply most expensive price sorting using a hybrid approach for MongoDB compatibility.
     */
    private function applyMostExpensiveSort(Builder $query): void
    {
        $query->with(['variations' => function ($variationQuery) {
            $variationQuery->select('product_id', 'price', 'sale_price');
        }]);
    }

    /**
     * Apply most sales sorting using invoice products as proxy.
     */
    private function applyMostSalesSort(Builder $query): void
    {
        $query->withCount('comments')->orderBy('comments_count', 'desc');
    }

    /**
     * Apply most popular sorting using multiple metrics.
     */
    private function applyMostPopularSort(Builder $query): void
    {
        $query->withCount('comments')->orderBy('comments_count', 'desc');
    }

    /**
     * Get minimum and maximum prices from all product variations (MongoDB-compatible).
     */
    private function getPriceStatistics(): array
    {
        $variations = ProductVariation::all(['price', 'sale_price']);

        if ($variations->isEmpty()) {
            return ['min_price' => 0, 'max_price' => 0];
        }

        $prices = $variations->map(function ($variation) {
            $salePrice = $variation->sale_price;
            $regularPrice = $variation->price;
            return ($salePrice && $salePrice < $regularPrice) ? $salePrice : $regularPrice;
        })->filter()->values();

        if ($prices->isEmpty()) {
            return ['min_price' => 0, 'max_price' => 0];
        }

        return [
            'min_price' => (int) $prices->min(),
            'max_price' => (int) $prices->max(),
        ];
    }

    /**
     * Sort products by their effective price (considering sale_price vs regular price).
     */
    private function sortProductsByPrice($products, string $sortDirection)
    {
        return $products->sort(function ($productA, $productB) use ($sortDirection) {
            $priceA = $this->getProductMinEffectivePrice($productA);
            $priceB = $this->getProductMinEffectivePrice($productB);

            if ($sortDirection === 'cheapest') {
                return $priceA <=> $priceB;
            } else {
                return $priceB <=> $priceA;
            }
        })->values();
    }

    /**
     * Get the minimum effective price for a product from its variations.
     */
    private function getProductMinEffectivePrice($product): float
    {
        if (!$product->relationLoaded('variations') || $product->variations->isEmpty()) {
            return 0;
        }

        $minPrice = $product->variations->map(function ($variation) {
            $salePrice = $variation->sale_price;
            $regularPrice = $variation->price;
            return ($salePrice && $salePrice < $regularPrice) ? $salePrice : $regularPrice;
        })->filter()->min();

        return $minPrice ?? 0;
    }

    /**
     * Manually paginate a collection.
     */
    private function paginateCollection($collection, int $perPage, int $page)
    {
        $total = $collection->count();
        $offset = ($page - 1) * $perPage;
        $items = $collection->slice($offset, $perPage)->values();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }
}
