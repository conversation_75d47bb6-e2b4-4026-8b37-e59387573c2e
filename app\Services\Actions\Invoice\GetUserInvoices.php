<?php

namespace App\Services\Actions\Invoice;

use App\Models\Shopping\Invoice;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class for retrieving invoices for a user.
 */
class GetUserInvoices
{
    /**
     * Retrieve all invoices for a user.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return Collection Collection of invoices
     */
    public function handle(array $data): Collection
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Retrieve all invoices for the user with their products and transactions, ordered by creation date (newest first)
        return Invoice::where('user_id', $userId)
            ->with([
                'products.details',
                'products.productVariation.product.gallery',
                'products.productVariation.attributes',
                'transactions'
            ]) // Eager load products, details, variations, and transactions for consistent display
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
