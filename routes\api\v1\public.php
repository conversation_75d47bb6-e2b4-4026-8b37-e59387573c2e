<?php
/**
 * Public API Routes
 *
 * This file contains all public API routes that do not require authentication.
 * These endpoints are accessible to all users without authentication.
 *
 * Base URI: /api/v1
 */

use App\Http\Controllers\Api\V1\Public\Product\ProductController;
use App\Http\Controllers\Api\V1\Public\Product\SimilarProductController;
use App\Http\Controllers\Api\V1\Public\Product\AttributeController;
use App\Http\Controllers\Api\V1\Public\Product\CategoryController;
use App\Http\Controllers\Api\V1\Public\Content\ArticleController;
use App\Http\Controllers\Api\V1\Public\Content\GuideController;
use App\Http\Controllers\Api\V1\Public\Shopping\VerifyTransactionController;
use App\Http\Controllers\Api\V1\Public\UserInteraction\CommentController;
use App\Http\Controllers\Api\V1\Public\UserInteraction\QuestionController;
use App\Http\Controllers\Api\V1\Public\Notification\PushNotificationController;
use App\Http\Controllers\Api\V1\Public\Notification\ClientTokenController;
use App\Http\Controllers\Api\V1\Public\Notification\TopicSubscriptionController;
use Illuminate\Support\Facades\Route;



/**
 * Product routes
 *
 * @unauthenticated
 */
Route::resource('products', ProductController::class)->only([
    'index',
    'show'
])->parameters([
            'products' => 'slug'
        ]);

/**
 * Product related resources routes
 *
 * @unauthenticated
 */
// Articles related to a product
Route::resource('products.articles', ArticleController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);
// Guides related to a product
Route::resource('products.guides', GuideController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);

// Comments related to a product
Route::resource('products.comments', CommentController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);

// Questions related to a product
Route::resource('products.questions', QuestionController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);

/**
 * Similar products
 *
 * @unauthenticated
 */
Route::resource('products.similar', SimilarProductController::class)->only([
    'index'
])->parameters([
            'products' => 'slug'
        ]);


/**
 * Verify transaction route
 *
 * @unauthenticated
 */
Route::resource('verify-transaction', VerifyTransactionController::class)
    ->only(['store']);


/**
 * Product attributes routes
 *
 * @unauthenticated
 */
Route::resource('attributes', AttributeController::class)
    ->only(['index']);

/**
 * Category routes
 *
 * @unauthenticated
 */
Route::resource('categories', CategoryController::class)
    ->only(['show'])
    ->parameters([
        'categories' => 'slug'
    ]);

/**
 * Notification routes
 *
 * @unauthenticated
 */
Route::resource('notifications', PushNotificationController::class)->only(['store']);
Route::resource('client-tokens', ClientTokenController::class)->only(['store']);
Route::resource('topic-subscriptions', TopicSubscriptionController::class)->only(['store']);
